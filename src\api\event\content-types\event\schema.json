{"kind": "collectionType", "collectionName": "events", "info": {"singularName": "event", "pluralName": "events", "displayName": "Event"}, "options": {"draftAndPublish": false}, "pluginOptions": {"content-manager": {"visible": true}, "content-type-builder": {"visible": true}}, "attributes": {"title": {"type": "string"}, "subject": {"type": "string"}, "topic": {"type": "string"}, "description": {"type": "text"}, "location": {"type": "string"}, "date": {"type": "date"}, "endDate": {"type": "date"}, "startTime": {"type": "time"}, "endTime": {"type": "time"}, "isMultiDay": {"type": "boolean"}, "hasMultipleSessions": {"type": "boolean"}, "autoRegistration": {"type": "boolean", "required": true, "default": false}, "attendanceMethod": {"type": "enumeration", "required": true, "default": "manual", "enum": ["manual", "kiosco_rapido"]}, "sendNotifications": {"type": "boolean"}, "eventStatus": {"type": "enumeration", "default": "programada", "enum": ["programada", "en-progreso", "completada", "cancelada"]}, "invitingDepartment": {"type": "relation", "relation": "manyToOne", "target": "api::department.department", "inversedBy": "events"}, "participants_invited": {"type": "relation", "relation": "manyToMany", "target": "plugin::users-permissions.user"}, "type": {"type": "relation", "relation": "manyToOne", "target": "api::event-type.event-type", "inversedBy": "events"}, "sessions": {"type": "component", "component": "session.attendance-session", "repeatable": true}, "attendanceRecords": {"type": "relation", "relation": "oneToMany", "target": "api::attendance-record.attendance-record", "mappedBy": "event"}}}