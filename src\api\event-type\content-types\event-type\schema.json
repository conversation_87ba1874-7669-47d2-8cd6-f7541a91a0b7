{"kind": "collectionType", "collectionName": "event_types", "info": {"singularName": "event-type", "pluralName": "event-types", "displayName": "Event Type", "description": "Tipos de eventos con configuraciones predeterminadas"}, "options": {"draftAndPublish": false}, "pluginOptions": {"content-manager": {"visible": true}, "content-type-builder": {"visible": true}}, "attributes": {"name": {"type": "string", "required": true, "unique": true}, "autoRegistrationDefault": {"type": "boolean", "required": true, "default": false}, "attendanceMethodDefault": {"type": "enumeration", "required": true, "default": "manual", "enum": ["manual", "kiosco_rapido"]}, "description": {"type": "text"}, "events": {"type": "relation", "relation": "oneToMany", "target": "api::event.event", "mappedBy": "type"}}}